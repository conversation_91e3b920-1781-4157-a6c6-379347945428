Using pre-set license
Built from '6000.1/staging' branch; Version is '6000.1.1f1 (7197418f847b) revision 7444289'; Using compiler version '194234433'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Professional' Language: 'en' Physical Memory: 32555 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1
Date: 2025-06-30T20:07:32Z

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.1.1f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker1
-projectPath
F:/Prototype Running Game
-logFile
Logs/AssetImportWorker1.log
-srvPort
63545
-job-worker-count
5
-background-job-worker-count
8
-gc-helper-count
1
-name
AssetImport
Successfully changed project path to: F:/Prototype Running Game
F:/Prototype Running Game
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [34916]  Target information:

Player connection [34916]  * "[IP] ************ [Port] 0 [Flags] 2 [Guid] 782761609 [EditorId] 782761609 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-RJGOOJ7) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [34916] Host joined multi-casting on [***********:54997]...
Player connection [34916] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 5
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 3.51 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.1.1f1 (7197418f847b)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.1.1f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path F:/Prototype Running Game/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:         Direct3D 12 [level 12.1]
    Renderer:        NVIDIA GeForce GTX 1660 SUPER (ID=0x21c4)
    Vendor:          NVIDIA
    VRAM:            5966 MB
    App VRAM Budget: 5198 MB
    Driver:          32.0.15.7680
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.1.1f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.1.1f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.1.1f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56696
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.1.1f1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.1.1f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.002720 seconds.
- Loaded All Assemblies, in  0.470 seconds
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.433 seconds
Domain Reload Profiling: 900ms
	BeginReloadAssembly (150ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (46ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (58ms)
	LoadAllAssembliesAndSetupDomain (198ms)
		LoadAssemblies (147ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (195ms)
			TypeCache.Refresh (192ms)
				TypeCache.ScanAssembly (177ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (434ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (379ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (62ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (77ms)
			ProcessInitializeOnLoadAttributes (171ms)
			ProcessInitializeOnLoadMethodAttributes (64ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.884 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.18 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.985 seconds
Domain Reload Profiling: 1866ms
	BeginReloadAssembly (185ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (36ms)
	RebuildCommonClasses (45ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (44ms)
	LoadAllAssembliesAndSetupDomain (594ms)
		LoadAssemblies (424ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (277ms)
			TypeCache.Refresh (206ms)
				TypeCache.ScanAssembly (189ms)
			BuildScriptInfoCaches (55ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (985ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (758ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (127ms)
			ProcessInitializeOnLoadAttributes (560ms)
			ProcessInitializeOnLoadMethodAttributes (56ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.01 seconds
Refreshing native plugins compatible for Editor in 1.25 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 221 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6406 unused Assets / (5.0 MB). Loaded Objects now: 7121.
Memory consumption went from 171.0 MB to 166.1 MB.
Total: 12.277500 ms (FindLiveObjects: 0.895300 ms CreateObjectMapping: 0.947400 ms MarkObjects: 7.124700 ms  DeleteObjects: 3.308200 ms)

========================================================================
Received Import Request.
  Time since last request: 40597.064807 seconds.
  path: Assets/Scripts/UI/InventoryUI.cs
  artifactKey: Guid(0ad5bac0f52275b48b975c3870727b31) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/UI/InventoryUI.cs using Guid(0ad5bac0f52275b48b975c3870727b31) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0b2e60359af984b92e5b6aab496992b7') in 0.0087145 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/Scripts/UI/HotbarSlotUI.cs
  artifactKey: Guid(c5a96826d762c5047a167fccae17702f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/UI/HotbarSlotUI.cs using Guid(c5a96826d762c5047a167fccae17702f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'aa4dad23e21c07a7ea80639d44a862c7') in 0.0005375 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.19 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6389 unused Assets / (4.9 MB). Loaded Objects now: 7115.
Memory consumption went from 138.5 MB to 133.6 MB.
Total: 12.407100 ms (FindLiveObjects: 0.852700 ms CreateObjectMapping: 1.330300 ms MarkObjects: 6.033400 ms  DeleteObjects: 4.188800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 2.25 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6372 unused Assets / (5.0 MB). Loaded Objects now: 7098.
Memory consumption went from 141.0 MB to 136.0 MB.
Total: 15.139000 ms (FindLiveObjects: 1.014300 ms CreateObjectMapping: 1.117600 ms MarkObjects: 7.153400 ms  DeleteObjects: 5.851600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 2.62 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6369 unused Assets / (4.6 MB). Loaded Objects now: 7095.
Memory consumption went from 143.5 MB to 138.9 MB.
Total: 16.559700 ms (FindLiveObjects: 1.007000 ms CreateObjectMapping: 1.487000 ms MarkObjects: 10.184000 ms  DeleteObjects: 3.879400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.43 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6368 unused Assets / (4.9 MB). Loaded Objects now: 7094.
Memory consumption went from 146.1 MB to 141.2 MB.
Total: 13.502800 ms (FindLiveObjects: 1.208600 ms CreateObjectMapping: 1.477700 ms MarkObjects: 7.588600 ms  DeleteObjects: 3.225500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.67 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6367 unused Assets / (4.6 MB). Loaded Objects now: 7093.
Memory consumption went from 148.7 MB to 144.0 MB.
Total: 11.080400 ms (FindLiveObjects: 0.878100 ms CreateObjectMapping: 1.011200 ms MarkObjects: 6.230100 ms  DeleteObjects: 2.958800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.863 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.16 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.954 seconds
Domain Reload Profiling: 1817ms
	BeginReloadAssembly (239ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (66ms)
	RebuildCommonClasses (40ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (34ms)
	LoadAllAssembliesAndSetupDomain (537ms)
		LoadAssemblies (426ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (218ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (191ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (954ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (728ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (130ms)
			ProcessInitializeOnLoadAttributes (518ms)
			ProcessInitializeOnLoadMethodAttributes (65ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (14ms)
Refreshing native plugins compatible for Editor in 1.37 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 24 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6374 unused Assets / (4.7 MB). Loaded Objects now: 7108.
Memory consumption went from 159.7 MB to 155.0 MB.
Total: 10.966900 ms (FindLiveObjects: 0.890600 ms CreateObjectMapping: 0.964400 ms MarkObjects: 6.237500 ms  DeleteObjects: 2.872600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.29 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6368 unused Assets / (4.6 MB). Loaded Objects now: 7109.
Memory consumption went from 160.6 MB to 156.0 MB.
Total: 11.488200 ms (FindLiveObjects: 0.863500 ms CreateObjectMapping: 1.018400 ms MarkObjects: 6.030600 ms  DeleteObjects: 3.573900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.853 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.45 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.978 seconds
Domain Reload Profiling: 1833ms
	BeginReloadAssembly (243ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (67ms)
	RebuildCommonClasses (45ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (32ms)
	LoadAllAssembliesAndSetupDomain (521ms)
		LoadAssemblies (428ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (214ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (188ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (978ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (764ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (128ms)
			ProcessInitializeOnLoadAttributes (558ms)
			ProcessInitializeOnLoadMethodAttributes (63ms)
			AfterProcessingInitializeOnLoad (7ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (15ms)
Refreshing native plugins compatible for Editor in 1.63 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6375 unused Assets / (4.9 MB). Loaded Objects now: 7111.
Memory consumption went from 163.0 MB to 158.1 MB.
Total: 11.398600 ms (FindLiveObjects: 0.878000 ms CreateObjectMapping: 1.017600 ms MarkObjects: 5.916300 ms  DeleteObjects: 3.585100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 231.725559 seconds.
  path: Assets/Scripts/Inventory/Model/NewScriptableObjectScript.cs
  artifactKey: Guid(0bc0d5637946df34a806c50af595b001) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Inventory/Model/NewScriptableObjectScript.cs using Guid(0bc0d5637946df34a806c50af595b001) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'dc641258510df44ea4ff7183bb48d499') in 0.0022627 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.38 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6368 unused Assets / (4.5 MB). Loaded Objects now: 7111.
Memory consumption went from 145.4 MB to 140.8 MB.
Total: 11.586700 ms (FindLiveObjects: 1.190300 ms CreateObjectMapping: 1.340000 ms MarkObjects: 5.938100 ms  DeleteObjects: 3.116600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 2.323321 seconds.
  path: Assets/Scripts/Inventory/Model/InventoryItem.cs
  artifactKey: Guid(0bc0d5637946df34a806c50af595b001) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Inventory/Model/InventoryItem.cs using Guid(0bc0d5637946df34a806c50af595b001) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1d7bac4146507ea40122db1b152c9ec1') in 0.0005931 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.836 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.79 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.965 seconds
Domain Reload Profiling: 1802ms
	BeginReloadAssembly (233ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (67ms)
	RebuildCommonClasses (42ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (35ms)
	LoadAllAssembliesAndSetupDomain (513ms)
		LoadAssemblies (419ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (204ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (176ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (965ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (754ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (133ms)
			ProcessInitializeOnLoadAttributes (544ms)
			ProcessInitializeOnLoadMethodAttributes (62ms)
			AfterProcessingInitializeOnLoad (7ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (15ms)
Refreshing native plugins compatible for Editor in 1.34 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6375 unused Assets / (4.6 MB). Loaded Objects now: 7113.
Memory consumption went from 145.2 MB to 140.6 MB.
Total: 11.098200 ms (FindLiveObjects: 1.065300 ms CreateObjectMapping: 1.060900 ms MarkObjects: 5.969200 ms  DeleteObjects: 3.000900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 8.658254 seconds.
  path: Assets/Scripts/Inventory/Model/InventoryItem.cs
  artifactKey: Guid(0bc0d5637946df34a806c50af595b001) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Inventory/Model/InventoryItem.cs using Guid(0bc0d5637946df34a806c50af595b001) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3cc6b4b481895e156e6eecc1d39e9f9a') in 0.0017647 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.59 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6367 unused Assets / (4.5 MB). Loaded Objects now: 7112.
Memory consumption went from 145.4 MB to 140.9 MB.
Total: 13.393200 ms (FindLiveObjects: 1.177300 ms CreateObjectMapping: 1.386100 ms MarkObjects: 6.946300 ms  DeleteObjects: 3.881700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.829 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.50 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.975 seconds
Domain Reload Profiling: 1806ms
	BeginReloadAssembly (225ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (64ms)
	RebuildCommonClasses (40ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (32ms)
	LoadAllAssembliesAndSetupDomain (520ms)
		LoadAssemblies (421ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (204ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (178ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (976ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (735ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (137ms)
			ProcessInitializeOnLoadAttributes (526ms)
			ProcessInitializeOnLoadMethodAttributes (58ms)
			AfterProcessingInitializeOnLoad (7ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (14ms)
Refreshing native plugins compatible for Editor in 1.38 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6374 unused Assets / (4.5 MB). Loaded Objects now: 7114.
Memory consumption went from 147.7 MB to 143.2 MB.
Total: 10.829800 ms (FindLiveObjects: 0.954800 ms CreateObjectMapping: 0.956100 ms MarkObjects: 5.933300 ms  DeleteObjects: 2.984100 ms)

Prepare: number of updated asset objects reloaded= 0
