{"name": "com.unity.ide.vscode", "displayName": "Visual Studio Code Editor", "description": "Code editor integration for supporting Visual Studio Code as code editor for unity. Adds support for generating csproj files for intellisense purposes, auto discovery of installations, etc.", "version": "1.2.5", "unity": "2019.2", "unityRelease": "0a12", "relatedPackages": {"com.unity.ide.vscode.tests": "1.2.5"}, "upmCi": {"footprint": "bc03b7bb076199fbe68649a647d0d32d98af6a0f"}, "repository": {"url": "https://github.com/Unity-Technologies/com.unity.ide.vscode.git", "type": "git", "revision": "b0740c80bfc2440527c317109f7c3d9100132722"}, "_fingerprint": "82e68656b7bdc5da8beb1fbaf1bb4ecf13456d51"}